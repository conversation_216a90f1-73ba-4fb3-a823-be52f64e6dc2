import sqlite3
import csv
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
from openpyxl.worksheet.views import SheetView, Selection

def export_filtered_data(db_files, output_folder, start_time, end_time, base_time, merge_output=True):
    """
    从多个数据库文件中提取数据并导出为Excel
    
    参数:
        db_files: 数据库文件路径列表
        output_folder: 输出文件夹路径
        start_time: 开始时间
        end_time: 结束时间
        base_time: 基准时间，用于在Excel中定位
        merge_output: 是否将多个数据库的数据合并到一个文件中
    """
    Path(output_folder).mkdir(parents=True, exist_ok=True)

    # 将时间范围格式化为YYYYMMDD-YYYYMMDD（如20220528-20220529）
    def format_time_range(start, end):
        try:
            start_clean = datetime.strptime(start, "%Y-%m-%d %H:%M:%S").strftime("%y%m%d%H%M")
            end_clean = datetime.strptime(end, "%Y-%m-%d %H:%M:%S").strftime("%d%H%M")
            return f"{start_clean}-{end_clean}"
        except ValueError:
            raise ValueError("时间格式必须为 'YYYY-MM-DD HH:MM:SS'")

    time_range = format_time_range(start_time, end_time)
    
    # 解析基准时间为datetime对象
    base_datetime = datetime.strptime(base_time, "%Y-%m-%d %H:%M:%S")
    
    if merge_output:
        # 合并模式：收集所有数据库的数据，然后合并输出
        all_data = []
        columns = None
        source_folders = []
        
        for db_path in db_files:
            try:
                db_file = Path(db_path)
                parent_folder_name = db_file.parent.name
                source_folders.append(parent_folder_name)
                
                # 连接数据库执行查询
                conn = sqlite3.connect(str(db_file))
                cursor = conn.cursor()

                # SQL查询
                sql = f"""
                SELECT 
                    (WELLDATE || ' ' || WELLTIME) AS date,
                    DEP, BITDEP, HOKHEI, DRITIME, WOB, HKLD, RPM, TOR, 
                    FLOWIN, FLOWOUT, SPP, CSIP, CW, MWIN, MWOUT, MTIN, 
                    MTOUT, OVERFLOW, TGAS, PITTOT, RIGSTA
                FROM data
                WHERE datetime(WELLDATE || ' ' || WELLTIME) 
                    BETWEEN '{start_time}' AND '{end_time}'
                ORDER BY datetime(WELLDATE || ' ' || WELLTIME)
                """

                cursor.execute(sql)
                rows = cursor.fetchall()
                
                if not columns:
                    columns = [desc[0] for desc in cursor.description]
                
                if rows:
                    all_data.extend(rows)
                    print(f"✅ 从 {db_file.name} 提取了 {len(rows)} 条数据")
                else:
                    print(f"⏩ 无数据: {db_file.name}")
                
                conn.close()
                
            except sqlite3.Error as e:
                print(f"❌ 数据库错误 ({db_path}): {str(e)}")
            except Exception as e:
                print(f"❌ 错误 ({db_path}): {str(e)}")
        
        # 如果有数据，生成合并的Excel文件
        if all_data:
            # 创建描述性的文件名
            if len(source_folders) == 1 or all(folder == source_folders[0] for folder in source_folders):
                # 只有一个数据源或所有数据源来自同一个文件夹时，直接使用该文件夹名
                output_prefix = source_folders[0]
            else:
                # 如果来自不同文件夹，使用第一个文件夹名加后缀
                output_prefix = f"{source_folders[0]}_等{len(source_folders)}个井"
            
            output_name = f"{output_prefix}_({time_range}).xlsx"
            excel_path = Path(output_folder) / output_name
            
            # 使用pandas创建DataFrame
            df = pd.DataFrame(all_data, columns=columns)
            
            # 查找与基准时间最接近的行索引
            # 将第一列(date)转换为datetime对象后计算与基准时间的差值
            df['date_obj'] = pd.to_datetime(df['date'])
            df['time_diff'] = abs(df['date_obj'] - base_datetime)
            closest_idx = df['time_diff'].idxmin()
            closest_row = closest_idx + 2  # 加2是因为Excel中有表头行和索引从1开始
            
            # 删除临时列
            df = df.drop(['date_obj', 'time_diff'], axis=1)
            
            # 保存到Excel
            writer = pd.ExcelWriter(excel_path, engine='openpyxl')
            df.to_excel(writer, index=False, sheet_name='数据')
            
            # 设置活动单元格为基准时间最接近的行
            worksheet = writer.sheets['数据']
            sheet_view = SheetView()
            active_cell = f"A{closest_row}"
            selection = Selection(activeCell=active_cell, sqref=active_cell)
            sheet_view.selection = [selection]
            worksheet.sheet_view.append(sheet_view)
            
            # 保存并关闭
            writer.close()
            
            print(f"✅ 合并导出成功: {excel_path} (共 {len(all_data)} 条数据)")
            print(f"⭐ 已设置活动单元格为与基准时间最接近的行: 第 {closest_row} 行")
        else:
            print("❌ 没有找到任何数据")
    
    else:
        # 原始模式：为每个数据库生成独立的Excel文件
        for db_path in db_files:
            try:
                db_file = Path(db_path)
                
                # 关键点：从路径中提取父文件夹名（如"D:\PyCharm\清洗3-4\自201H35-2" → "自201H35-2"）
                parent_folder_name = db_file.parent.name  # 直接获取父目录名
                
                # 构建输出文件名
                output_name = f"{parent_folder_name}_({time_range}).xlsx"
                excel_path = Path(output_folder) / output_name

                # 连接数据库执行查询
                conn = sqlite3.connect(str(db_file))
                cursor = conn.cursor()

                # SQL查询（使用参数化时间条件）
                sql = f"""
                SELECT 
                    (WELLDATE || ' ' || WELLTIME) AS date,
                    DEP, BITDEP, HOKHEI, DRITIME, WOB, HKLD, RPM, TOR, 
                    FLOWIN, FLOWOUT, SPP, CSIP, CW, MWIN, MWOUT, MTIN, 
                    MTOUT, OVERFLOW, TGAS, PITTOT, RIGSTA
                FROM data
                WHERE datetime(WELLDATE || ' ' || WELLTIME) 
                    BETWEEN '{start_time}' AND '{end_time}'
                ORDER BY datetime(WELLDATE || ' ' || WELLTIME)
                """

                cursor.execute(sql)
                rows = cursor.fetchall()
                columns = [desc[0] for desc in cursor.description]

                if not rows:
                    print(f"⏩ 无数据: {db_file.name}")
                    continue

                # 使用pandas创建DataFrame
                df = pd.DataFrame(rows, columns=columns)
                
                # 查找与基准时间最接近的行索引
                df['date_obj'] = pd.to_datetime(df['date'])
                df['time_diff'] = abs(df['date_obj'] - base_datetime)
                closest_idx = df['time_diff'].idxmin()
                closest_row = closest_idx + 2  # 加2是因为Excel中有表头行和索引从1开始
                
                # 删除临时列
                df = df.drop(['date_obj', 'time_diff'], axis=1)
                
                # 保存到Excel
                writer = pd.ExcelWriter(excel_path, engine='openpyxl')
                df.to_excel(writer, index=False, sheet_name='数据')
                
                # 设置活动单元格为基准时间最接近的行
                worksheet = writer.sheets['数据']
                sheet_view = SheetView()
                active_cell = f"A{closest_row}"
                selection = Selection(activeCell=active_cell, sqref=active_cell)
                sheet_view.selection = [selection]
                worksheet.sheet_views.append(sheet_view)
                
                # 保存并关闭
                writer.close()
                
                print(f"✅ 导出成功: {excel_path}")
                print(f"⭐ 已设置活动单元格为与基准时间最接近的行: 第 {closest_row} 行")

            except sqlite3.Error as e:
                print(f"❌ 数据库错误: {str(e)}")
            except Exception as e:
                print(f"❌ 错误: {str(e)}")
            finally:
                if 'conn' in locals():
                    conn.close()

def calculate_time_range(time_base):
    """
    根据基准时间计算时间范围：
    - 开始时间：基准时间前两天
    - 结束时间：基准时间后一天
    
    参数:
        time_base: 字符串，格式为 "YYYY-MM-DD HH:MM:SS"
    
    返回:
        start_time, end_time: 两个字符串，格式同上
    """
    try:
        # 将字符串转换为datetime对象
        base_time = datetime.strptime(time_base, "%Y-%m-%d %H:%M:%S")
        
        # 计算开始时间（前两天）
        start_time = base_time - timedelta(days=2)
        
        # 计算结束时间（后一天）
        end_time = base_time + timedelta(days=1)
        
        # 转换回字符串格式
        return start_time.strftime("%Y-%m-%d %H:%M:%S"), end_time.strftime("%Y-%m-%d %H:%M:%S")
    
    except ValueError:
        raise ValueError("时间格式必须为 'YYYY-MM-DD HH:MM:SS'")

if __name__ == "__main__":
    # ===== 配置参数 =====
    TARGET_FILES = [
        r"D:\采数据\PROCESS\泸203H153-1\02201.db",
        # 在此添加更多数据库文件
        # r"D:\采数据\PROCESS\泸203H91-4\02204.db" 
    ]
    
    # 基准时间
    TIME_BASE = "2022-01-17 05:00:01"
    
    # 自动计算时间范围
    TIME_START, TIME_END = calculate_time_range(TIME_BASE)
    
    print(f"基准时间: {TIME_BASE}")
    print(f"开始时间: {TIME_START}")
    print(f"结束时间: {TIME_END}")
    
    OUTPUT_FOLDER = r"D:\采数据\PROCESS\clean"  # 所有文件输出到此目录
    
    # 是否合并输出为一个文件
    MERGE_OUTPUT = True
    
    # ===== 执行导出 =====
    export_filtered_data(
        db_files=TARGET_FILES,
        output_folder=OUTPUT_FOLDER,
        start_time=TIME_START,
        end_time=TIME_END,
        base_time=TIME_BASE,
        merge_output=MERGE_OUTPUT
    )