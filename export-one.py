import sqlite3
import csv
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
from matplotlib.ticker import ScalarFormatter
from matplotlib import dates as mdates
from datetime import  timedelta

# ========== 中文字体配置 ==========
plt.rcParams['font.sans-serif'] = ['SimHei']  # Windows系统字体
plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示异常

# ========== 中英文对照字典 ==========
""" name_map = {
    # 原有特征
    'DEP': '井深 (DEP)',
    'BITDEP': '钻头位置 (BITDEP)',
    'HOKHEI': '大钩负荷 (HOKHEI)',
    'DRITIME': '钻时 (DRITIME)',
    'WOB': '钻压 (WOB)',
    'HKLD': '大钩高度 (HKLD)',
    'RPM': '转速 (RPM)',
    'TOR': '扭矩 (TOR)',
    
    # 新增特征
    'FLOWIN': '入口流量 (FLOWIN)',
    'FLOWOUT': '出口流量 (FLOWOUT)',
    'SPP': '泵压 (SPP)',
    'CSIP': '套压 (CSIP)'
} """
name_map = {
    # 原有特征
    'DEP': '井深',
    'BITDEP': '钻头位置',
    'HOKHEI': '大钩负荷',
    'DRITIME': '钻时',
    'WOB': '钻压',
    'HKLD': '大钩高度',
    'RPM': '转速',
    'TOR': '扭矩',
    
    # 新增特征
    'FLOWIN': '入口流量',
    'FLOWOUT': '出口流量',
    'SPP': '泵压',
    'CSIP': '套压'
}

def process_data_and_plot(db_files, base_output_folder, start_time, end_time):
    """支持中英文标签的完整流程"""
    plt.style.use('seaborn-v0_8-colorblind')
    start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
    end_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
    time_span = end_dt - start_dt
    # 根据时间跨度选择刻度配置
    if time_span >= timedelta(days=1):
        # 配置1：主刻度每小时，次刻度每小时（适合长时间跨度）
        major_interval = 2  # 主刻度间隔（小时）
        minor_interval = 30 # 次刻度间隔（分钟）
        print("📅 时间跨度≥1天，启用每小时主/次刻度配置")
    else:
        # 配置2：主刻度每小时，次刻度每10分钟（适合短时间跨度）
        major_interval = 1
        minor_interval = 10
        print("⏱️ 时间跨度<1天，启用主刻度小时+次刻度10分钟配置")

    # 时间范围格式化工具（输出示例：22030400-22030504）
    def format_time_range(start, end):
        try:
            start_dt = datetime.strptime(start, "%Y-%m-%d %H:%M:%S")
            end_dt = datetime.strptime(end, "%Y-%m-%d %H:%M:%S")
#            return f"{start_dt.strftime('%y%m%d%H%M')}-{end_dt.strftime('%y%m%d%H%M')}"
            return f"{start_dt.strftime('%y%m%d%H%M')}-{end_dt.strftime('%d%H%M')}"
        except ValueError:
            raise ValueError("时间格式必须为 'YYYY-MM-DD HH:MM:SS'")

    time_range = format_time_range(start_time, end_time)

    for db_path in db_files:
        try:
            db_file = Path(db_path)
            parent_name = db_file.parent.name
            
            # ========== 动态构建输出路径 ==========
#            output_folder = Path(base_output_folder) / parent_name
            output_folder = Path(base_output_folder)
            output_folder.mkdir(parents=True, exist_ok=True)
            
            # ========== 第一步：生成CSV ==========
            csv_name = f"{parent_name}_({time_range}).csv"
            csv_path = output_folder / csv_name

            # 连接数据库
            conn = sqlite3.connect(str(db_file), timeout=30)
            cursor = conn.cursor()

            # 调试：打印数据库结构
            print(f"\n🔍 数据库结构检查 [{db_file.name}]")
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            print(f"存在的表: {tables}")

            if ('data',) not in tables:
                print(f"❌ 关键表 'data' 不存在于数据库中")
                continue

            # 构建SQL查询（参数化时间条件）
            sql = """
            SELECT 
                (WELLDATE || ' ' || WELLTIME) AS date,
                DEP, BITDEP, HOKHEI, DRITIME, WOB, HKLD, RPM, TOR, 
                FLOWIN, FLOWOUT, SPP, CSIP, RIGSTA
            FROM data
            WHERE datetime(WELLDATE || ' ' || WELLTIME) 
                BETWEEN ? AND ?
            """
            print(f"\n⚡ 执行的SQL语句:\n{sql}")
            print(f"⌛ 时间参数: {start_time} → {end_time}")

            # 执行查询
            cursor.execute(sql, (start_time, end_time))
            rows = cursor.fetchall()
            
            if not rows:
                print(f"⏩ 查询结果为空，跳过文件生成")
                continue

            # 写入CSV
            with open(csv_path, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                writer.writerow([i[0] for i in cursor.description])
                writer.writerows(rows)
            print(f"✅ CSV导出成功 ({len(rows)} 行数据): {csv_path}")
            
            # ========== 第二步：生成趋势图 ==========
            # 生成图片路径
            img_name = f"{parent_name}_({time_range}).png"
            img_path = output_folder / img_name
            
            # 从CSV读取数据
            df = pd.read_csv(csv_path)
            df['date'] = pd.to_datetime(df['date'])
            
            # 配置图表
            features = [
                'DEP', 'BITDEP', 'HOKHEI', 'DRITIME',
                'WOB', 'HKLD', 'RPM', 'TOR',
                'FLOWIN', 'FLOWOUT', 'SPP', 'CSIP'
            ]
            fig, axes = plt.subplots(len(features), 1, figsize=(22, 1.2*len(features)), sharex=True)
            
            # 绘制曲线
            colors = plt.get_cmap('tab10')
            for i, col in enumerate(features):
                axes[i].plot(df['date'], df[col], color=colors(i%10), label=name_map[col])
                axes[i].set_ylabel(name_map[col], rotation=0, labelpad=20, ha='right')
#                axes[i].legend(loc='upper right', fontsize=10)
                axes[i].legend(
                  loc='best',         # 自动选择图例位置（避免遮挡曲线）
                  frameon=True,     # 去掉图例边框（更简洁）
#                 fontsize='small'   # 调小字体（节省空间）
                  )
                axes[i].yaxis.set_major_formatter(ScalarFormatter(useOffset=False))
                axes[i].ticklabel_format(style='plain', axis='y')
            
            # 调整布局
            for ax in axes:
#              ax.xaxis.set_major_locator(mdates.HourLocator(interval=1))  # 每2小时主刻度
#              ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))  # 格式示例: 03-04 00:00
#              ax.xaxis.set_minor_locator(mdates.HourLocator(interval=1))  # 每小时次刻度（可选）
              ax.xaxis.set_major_locator(mdates.HourLocator(interval=major_interval))
              ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
              if minor_interval < 60:
            # 分钟级次刻度
                ax.xaxis.set_minor_locator(mdates.MinuteLocator(byminute=range(0, 60, minor_interval)))
              else:
            # 小时级次刻度
                ax.xaxis.set_minor_locator(mdates.HourLocator(interval=1))
            axes[-1].tick_params(axis='x', rotation=0)
            fig.suptitle(f'{parent_name} 参数趋势 ({time_range})', y=0.98)
#            plt.tight_layout(rect=[0, 0, 0, 0.96])
            plt.tight_layout(h_pad=0.5)  # 增加垂直间距防止标签重叠
            
            # 保存图片
            plt.savefig(img_path, bbox_inches='tight', dpi=300)
            plt.close()
            print(f"✅ 趋势图生成成功: {img_path}")

        except sqlite3.OperationalError as e:
            print(f"❌ 数据库操作错误: {str(e)}")
            if "no such table" in str(e):
                print("👉 请确认数据库中存在 'data' 表")
        except Exception as e:
            print(f"❌ 未知错误: {str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

if __name__ == "__main__":
    # ===== 配置参数 =====
    config = {
        "db_files": [
            r"D:\采数据\PROCESS\自201H54-1\02209.db"
            ],
            "base_output_folder": r"D:\采数据\PROCESS\clean514",
            "start_time": "2022-09-09 09:25:00",
            "end_time": "2022-09-10 10:25:00"
        }
    process_data_and_plot(**config)

    # ===== 执行流程 =====
    process_data_and_plot(**config)