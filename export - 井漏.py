import sqlite3
import csv
from pathlib import Path
from datetime import datetime, timedelta

def export_filtered_data(db_files, output_folder, start_time, end_time, merge_output=True):
    """
    从多个数据库文件中提取数据并导出为CSV
    
    参数:
        db_files: 数据库文件路径列表
        output_folder: 输出文件夹路径
        start_time: 开始时间
        end_time: 结束时间
        merge_output: 是否将多个数据库的数据合并到一个CSV文件中
    """
    Path(output_folder).mkdir(parents=True, exist_ok=True)

    # 将时间范围格式化为YYYYMMDD-YYYYMMDD（如20220528-20220529）
    def format_time_range(start, end):
        try:
            start_clean = datetime.strptime(start, "%Y-%m-%d %H:%M:%S").strftime("%y%m%d%H%M")
            end_clean = datetime.strptime(end, "%Y-%m-%d %H:%M:%S").strftime("%d%H%M")
            return f"{start_clean}-{end_clean}"
        except ValueError:
            raise ValueError("时间格式必须为 'YYYY-MM-DD HH:MM:SS'")

    time_range = format_time_range(start_time, end_time)
    
    if merge_output:
        # 合并模式：收集所有数据库的数据，然后合并输出
        all_data = []
        columns = None
        source_folders = []
        
        for db_path in db_files:
            try:
                db_file = Path(db_path)
                parent_folder_name = db_file.parent.name
                source_folders.append(parent_folder_name)
                
                # 连接数据库执行查询
                conn = sqlite3.connect(str(db_file))
                cursor = conn.cursor()

                # SQL查询
                sql = f"""
                SELECT 
                    (WELLDATE || ' ' || WELLTIME) AS date,
                    DEP, BITDEP, HOKHEI, DRITIME, WOB, HKLD, RPM, TOR, 
                    FLOWIN, FLOWOUT, SPP, CSIP, CW, MWIN, MWOUT, MTIN, 
                    MTOUT, OVERFLOW, TGAS, PITTOT, RIGSTA
                FROM data
                WHERE datetime(WELLDATE || ' ' || WELLTIME) 
                    BETWEEN '{start_time}' AND '{end_time}'
                ORDER BY datetime(WELLDATE || ' ' || WELLTIME)
                """

                cursor.execute(sql)
                rows = cursor.fetchall()
                
                if not columns:
                    columns = [desc[0] for desc in cursor.description]
                
                if rows:
                    all_data.extend(rows)
                    print(f"✅ 从 {db_file.name} 提取了 {len(rows)} 条数据")
                else:
                    print(f"⏩ 无数据: {db_file.name}")
                
                conn.close()
                
            except sqlite3.Error as e:
                print(f"❌ 数据库错误 ({db_path}): {str(e)}")
            except Exception as e:
                print(f"❌ 错误 ({db_path}): {str(e)}")
        
        # 如果有数据，生成合并的CSV文件
        if all_data:
            # 创建描述性的文件名
            if len(source_folders) == 1 or all(folder == source_folders[0] for folder in source_folders):
                # 只有一个数据源或所有数据源来自同一个文件夹时，直接使用该文件夹名
                output_prefix = source_folders[0]
            else:
                # 如果来自不同文件夹，使用第一个文件夹名加后缀
                output_prefix = f"{source_folders[0]}_等{len(source_folders)}个井"
            
            output_name = f"{output_prefix}_({time_range}).csv"
            csv_path = Path(output_folder) / output_name
            
            # 写入合并的CSV
            with open(csv_path, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                writer.writerow(columns)
                writer.writerows(all_data)
            
            print(f"✅ 合并导出成功: {csv_path} (共 {len(all_data)} 条数据)")
        else:
            print("❌ 没有找到任何数据")
    
    else:
        # 原始模式：为每个数据库生成独立的CSV文件
        for db_path in db_files:
            try:
                db_file = Path(db_path)
                
                # 关键点：从路径中提取父文件夹名（如"D:\PyCharm\清洗3-4\自201H35-2" → "自201H35-2"）
                parent_folder_name = db_file.parent.name  # 直接获取父目录名
                
                # 构建输出文件名
                output_name = f"{parent_folder_name}_({time_range}).csv"
                csv_path = Path(output_folder) / output_name

                # 连接数据库执行查询
                conn = sqlite3.connect(str(db_file))
                cursor = conn.cursor()

                # SQL查询（使用参数化时间条件）
                sql = f"""
                SELECT 
                    (WELLDATE || ' ' || WELLTIME) AS date,
                    DEP, BITDEP, HOKHEI, DRITIME, WOB, HKLD, RPM, TOR, 
                    FLOWIN, FLOWOUT, SPP, CSIP, CW, MWIN, MWOUT, MTIN, 
                    MTOUT, OVERFLOW, TGAS, PITTOT, RIGSTA
                FROM data
                WHERE datetime(WELLDATE || ' ' || WELLTIME) 
                    BETWEEN '{start_time}' AND '{end_time}'
                """

                cursor.execute(sql)
                rows = cursor.fetchall()
                columns = [desc[0] for desc in cursor.description]

                if not rows:
                    print(f"⏩ 无数据: {db_file.name}")
                    continue

                # 写入CSV
                with open(csv_path, 'w', newline='', encoding='utf-8-sig') as f:
                    writer = csv.writer(f)
                    writer.writerow(columns)
                    writer.writerows(rows)

                print(f"✅ 导出成功: {csv_path}")

            except sqlite3.Error as e:
                print(f"❌ 数据库错误: {str(e)}")
            except Exception as e:
                print(f"❌ 错误: {str(e)}")
            finally:
                if 'conn' in locals():
                    conn.close()

def calculate_time_range(time_base):
    """
    根据基准时间计算时间范围：
    - 开始时间：基准时间前两天
    - 结束时间：基准时间后一天
    
    参数:
        time_base: 字符串，格式为 "YYYY-MM-DD HH:MM:SS"
    
    返回:
        start_time, end_time: 两个字符串，格式同上
    """
    try:
        # 将字符串转换为datetime对象
        base_time = datetime.strptime(time_base, "%Y-%m-%d %H:%M:%S")
        
        # 计算开始时间（前两天）
        start_time = base_time - timedelta(days=2)
        
        # 计算结束时间（后一天）
        end_time = base_time + timedelta(days=1)
        
        # 转换回字符串格式
        return start_time.strftime("%Y-%m-%d %H:%M:%S"), end_time.strftime("%Y-%m-%d %H:%M:%S")
    
    except ValueError:
        raise ValueError("时间格式必须为 'YYYY-MM-DD HH:MM:SS'")

if __name__ == "__main__":
    # ===== 配置参数 =====
    TARGET_FILES = [
        r"D:\采数据\PROCESS\长宁H30-5\02204.db",
        # 在此添加更多数据库文件
#        r"D:\采数据\PROCESS\泸203H91-4\02206.db" 
    ]
    
    # 基准时间
    TIME_BASE = "2022-04-06 00:12:07"
    
    # 自动计算时间范围
    TIME_START, TIME_END = calculate_time_range(TIME_BASE)
    
    print(f"基准时间: {TIME_BASE}")
    print(f"开始时间: {TIME_START}")
    print(f"结束时间: {TIME_END}")
    
    OUTPUT_FOLDER = r"D:\采数据\PROCESS\clean"  # 所有文件输出到此目录
    
    # 是否合并输出为一个CSV文件
    MERGE_OUTPUT = True
    
    # ===== 执行导出 =====
    export_filtered_data(
        db_files=TARGET_FILES,
        output_folder=OUTPUT_FOLDER,
        start_time=TIME_START,
        end_time=TIME_END,
        merge_output=MERGE_OUTPUT
    )