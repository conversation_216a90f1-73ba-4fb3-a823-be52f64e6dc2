import sqlite3
import csv
from pathlib import Path
from datetime import datetime

def export_filtered_data(db_files, output_folder, start_time, end_time):
    """动态生成文件名：父文件夹名_时间范围.csv"""
    Path(output_folder).mkdir(parents=True, exist_ok=True)

    # 将时间范围格式化为YYYYMMDD-YYYYMMDD（如20220528-20220529）
    def format_time_range(start, end):
        try:
            start_clean = datetime.strptime(start, "%Y-%m-%d %H:%M:%S").strftime("%Y%m%d")
            end_clean = datetime.strptime(end, "%Y-%m-%d %H:%M:%S").strftime("%Y%m%d")
            return f"{start_clean}-{end_clean}"
        except ValueError:
            raise ValueError("时间格式必须为 'YYYY-MM-DD HH:MM:SS'")

    time_range = format_time_range(start_time, end_time)

    for db_path in db_files:
        try:
            db_file = Path(db_path)
            
            # 关键点：从路径中提取父文件夹名（如"D:\PyCharm\清洗3-4\自201H35-2" → "自201H35-2"）
            parent_folder_name = db_file.parent.name  # 直接获取父目录名
            
            # 构建输出文件名
            output_name = f"{parent_folder_name}_{time_range}.csv"
            csv_path = Path(output_folder) / output_name

            # 连接数据库执行查询
            conn = sqlite3.connect(str(db_file))
            cursor = conn.cursor()

            # SQL查询（使用参数化时间条件）
            sql = f"""
            SELECT
                (WELLDATE || ' ' || WELLTIME) AS date,
                DEP, BITDEP, HOKHEI, DRITIME, WOB, HKLD, RPM, TOR, FLOWIN, FLOWOUT, SPP, CSIP, RIGSTA
            FROM data
            WHERE datetime(WELLDATE || ' ' || WELLTIME) 
                BETWEEN '{start_time}' AND '{end_time}'
            """

            cursor.execute(sql)
            rows = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]

            if not rows:
                print(f"⏩ 无数据: {db_file.name}")
                continue

            # 写入CSV
            with open(csv_path, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                writer.writerow(columns)
                writer.writerows(rows)

            print(f"✅ 导出成功: {csv_path}")

        except sqlite3.Error as e:
            print(f"❌ 数据库错误: {str(e)}")
        except Exception as e:
            print(f"❌ 错误: {str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

if __name__ == "__main__":
    # ===== 配置参数 =====
    TARGET_FILES = [
        r"D:\PyCharm\清洗3-4\自201H35-2\02203.db"  # 支持多个文件，会自动合并时间范围
    ]
    
    TIME_START = "2022-03-04 00:00:00"
    TIME_END = "2022-03-05 04:00:00"
    
    OUTPUT_FOLDER = r"D:\PyCharm\清洗3-4\输出结果"  # 所有文件输出到此目录
    
    # ===== 执行导出 =====
    export_filtered_data(
        db_files=TARGET_FILES,
        output_folder=OUTPUT_FOLDER,
        start_time=TIME_START,
        end_time=TIME_END
    )