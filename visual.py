import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
from matplotlib.ticker import ScalarFormatter
from matplotlib import dates as mdates
# ========== 中文字体配置 ==========
plt.rcParams['font.sans-serif'] = ['SimHei']  # Windows系统字体
plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示异常

def generate_trend_plot(
    csv_path: str, 
    output_folder: str, 
    time_range: str = None,
    figsize: tuple = (22, 30),
    dpi: int = 300
):
    """
    :param csv_path: CSV文件路径
    :param output_folder: 图片输出目录
    :param time_range: 时间范围字符串（用于标题）
    :param figsize: 图表尺寸
    :param dpi: 图片分辨率
    """
    # ========== 参数校验 ==========
    csv_file = Path(csv_path)
    if not csv_file.exists():
        raise FileNotFoundError(f"CSV文件不存在: {csv_path}")
    
    name_map = {
    # 原有特征
    'DEP': '井深',
    'BITDEP': '钻头位置',
    'HOKHEI': '大钩负荷',
    'DRITIME': '钻时',
    'WOB': '钻压',
    'HKLD': '大钩高度',
    'RPM': '转速',
    'TOR': '扭矩',
    
    # 新增特征
    'FLOWIN': '入口流量',
    'FLOWOUT': '出口流量',
    'SPP': '泵压',
    'CSIP': '套压'
}

    # ========== 配置输出路径 ==========
    output_dir = Path(output_folder)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成图片文件名
    parent_name = csv_file.stem.split('_')[0]  # 从文件名提取父目录名
    img_name = f"{parent_name}_{csv_file.stem.split('_')[-1]}.png"
    img_path = output_dir / img_name

    # ========== 数据准备 ==========
    try:
        df = pd.read_csv(csv_file)
        df['date'] = pd.to_datetime(df['date'])
    except Exception as e:
        raise ValueError(f"CSV文件读取失败: {str(e)}")

    # ========== 图表配置 ==========
    plt.style.use('seaborn-v0_8-colorblind')
    features = [
        'DEP', 'BITDEP', 'HOKHEI', 'DRITIME',
        'WOB', 'HKLD', 'RPM', 'TOR',
        'FLOWIN', 'FLOWOUT', 'SPP', 'CSIP'
    ]
    
    # ========== 创建图表 ==========
    fig, axes = plt.subplots(len(features), 1, figsize=(22, 1.2*len(features)), sharex=True)
    
    # 绘制每个特征
    colors = plt.get_cmap('tab10')
    for i, col in enumerate(features):
        axes[i].plot(df['date'], df[col], color=colors(i%10), label=name_map[col])
        axes[i].set_ylabel(name_map[col], rotation=0, labelpad=20, ha='right')
        axes[i].legend(loc='best', frameon=True)
        axes[i].yaxis.set_major_formatter(ScalarFormatter(useOffset=False))
        axes[i].ticklabel_format(style='plain', axis='y')

    # ========== 时间轴格式设置 ==========
    for ax in axes:
        ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
        ax.xaxis.set_minor_locator(mdates.HourLocator(interval=1))

    # 调整底部标签角度
    axes[-1].tick_params(axis='x', rotation=0)

    # ========== 标题和布局 ==========
    title = f'{parent_name} 参数趋势'
    if time_range:
        title += f' ({time_range})'
    fig.suptitle(title, y=0.98)
    plt.tight_layout(h_pad=1.5)

    # ========== 保存输出 ==========
    plt.savefig(img_path, bbox_inches='tight', dpi=dpi)
    plt.close()
    print(f"✅ 趋势图生成成功: {img_path}")
    return str(img_path)

# ===== 使用示例 =====
if __name__ == "__main__":
    # 直接使用现有CSV生成图表
    generate_trend_plot(
        csv_path=r"D:\采数据\PROCESS\clean505\自222_2312030730.csv",
        output_folder=r"D:\pycharm",
        time_range="2023-12-03 07:30:00",
        figsize=(22, 25),
        dpi=200
    )