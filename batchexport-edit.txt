import sqlite3
import csv
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
import os
import calendar

def export_filtered_data(db_file, output_folder, start_time, end_time, base_time):
    """
    从数据库文件中提取数据并导出为CSV
    
    参数:
        db_file: 数据库文件路径
        output_folder: 输出文件夹路径
        start_time: 开始时间
        end_time: 结束时间
        base_time: 基准时间，用于参考
    """
    Path(output_folder).mkdir(parents=True, exist_ok=True)

    # 将时间范围格式化为YYYYMMDD-YYYYMMDD（如20220528-20220529）
    def format_time_range(start, end):
        try:
            start_clean = datetime.strptime(start, "%Y-%m-%d %H:%M:%S").strftime("%y%m%d%H%M")
            end_clean = datetime.strptime(end, "%Y-%m-%d %H:%M:%S").strftime("%d%H%M")
            return f"{start_clean}-{end_clean}"
        except ValueError:
            raise ValueError("时间格式必须为 'YYYY-MM-DD HH:MM:SS'")

    time_range = format_time_range(start_time, end_time)
    
    try:
        db_path = Path(db_file)
        if not db_path.exists():
            print(f"❌ 数据库文件不存在: {db_file}")
            return False
            
        parent_folder_name = db_path.parent.name
        
        # 连接数据库执行查询
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()

        # SQL查询
        sql = f"""
        SELECT 
            (WELLDATE || ' ' || WELLTIME) AS date,
            DEP, BITDEP, HOKHEI, DRITIME, WOB, HKLD, RPM, TOR, 
            FLOWIN, FLOWOUT, SPP, CSIP, CW, MWIN, MWOUT, MTIN, 
            MTOUT, OVERFLOW, TGAS, PITTOT, RIGSTA
        FROM data
        WHERE datetime(WELLDATE || ' ' || WELLTIME) 
            BETWEEN '{start_time}' AND '{end_time}'
        ORDER BY datetime(WELLDATE || ' ' || WELLTIME)
        """

        cursor.execute(sql)
        rows = cursor.fetchall()
        columns = [desc[0] for desc in cursor.description]
        
        if not rows:
            print(f"⏩ 无数据: {db_path.name}")
            conn.close()
            return False

        # 构建输出文件名
        output_name = f"{parent_folder_name}_({time_range}).csv"
        csv_path = Path(output_folder) / output_name
        
        # 写入CSV文件
        with open(csv_path, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            writer.writerow(columns)
            writer.writerows(rows)
        
        conn.close()
        
        print(f"✅ 导出成功: {csv_path} (共 {len(rows)} 条数据)")
        return True
        
    except sqlite3.Error as e:
        print(f"❌ 数据库错误 ({db_file}): {str(e)}")
    except Exception as e:
        print(f"❌ 错误 ({db_file}): {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        if 'conn' in locals():
            conn.close()
    
    return False

def export_filtered_data_cross_month(well_path, output_folder, start_time, end_time, base_time):
    """
    处理跨月时间范围的数据导出，查询多个月份的数据库文件并合并结果
    
    参数:
        well_path: 井文件夹路径
        output_folder: 输出文件夹路径
        start_time: 开始时间
        end_time: 结束时间
        base_time: 基准时间
    """
    Path(output_folder).mkdir(parents=True, exist_ok=True)

    # 将时间范围格式化为YYYYMMDD-YYYYMMDD（如20220528-20220529）
    def format_time_range(start, end):
        try:
            start_clean = datetime.strptime(start, "%Y-%m-%d %H:%M:%S").strftime("%y%m%d%H%M")
            end_clean = datetime.strptime(end, "%Y-%m-%d %H:%M:%S").strftime("%d%H%M")
            return f"{start_clean}-{end_clean}"
        except ValueError:
            raise ValueError("时间格式必须为 'YYYY-MM-DD HH:MM:SS'")

    time_range = format_time_range(start_time, end_time)
    
    # 解析时间范围
    start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
    end_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
    
    # 确定需要查询的所有月份
    months_to_query = []
    current = datetime(start_dt.year, start_dt.month, 1)
    
    while current <= end_dt:
        months_to_query.append((current.year, current.month))
        # 移动到下个月
        if current.month == 12:
            current = datetime(current.year + 1, 1, 1)
        else:
            current = datetime(current.year, current.month + 1, 1)
    
    # 从每个月份的数据库中提取数据
    all_rows = []
    columns = None
    parent_folder_name = Path(well_path).name
    
    for year, month in months_to_query:
        # 生成数据库文件名: 0YYMM.db
        db_filename = f"0{str(year)[2:4]}{month:02d}.db"
        db_path = os.path.join(well_path, db_filename)
        
        if not os.path.exists(db_path):
            print(f"⚠️ 数据库文件不存在，跳过: {db_path}")
            continue
        
        try:
            # 连接数据库执行查询
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # SQL查询
            sql = f"""
            SELECT 
                (WELLDATE || ' ' || WELLTIME) AS date,
                DEP, BITDEP, HOKHEI, DRITIME, WOB, HKLD, RPM, TOR, 
                FLOWIN, FLOWOUT, SPP, CSIP, CW, MWIN, MWOUT, MTIN, 
                MTOUT, OVERFLOW, TGAS, PITTOT, RIGSTA
            FROM data
            WHERE datetime(WELLDATE || ' ' || WELLTIME) 
                BETWEEN '{start_time}' AND '{end_time}'
            ORDER BY datetime(WELLDATE || ' ' || WELLTIME)
            """

            cursor.execute(sql)
            rows = cursor.fetchall()
            
            if not columns:
                columns = [desc[0] for desc in cursor.description]
            
            if rows:
                all_rows.extend(rows)
                print(f"✅ 从 {db_filename} 提取了 {len(rows)} 条数据")
            else:
                print(f"⏩ 无数据: {db_filename}")
            
            conn.close()
            
        except sqlite3.Error as e:
            print(f"❌ 数据库错误 ({db_path}): {str(e)}")
        except Exception as e:
            print(f"❌ 错误 ({db_path}): {str(e)}")
    
    # 如果有数据，写入CSV
    if all_rows and columns:
        # 对所有数据按时间排序
        all_rows.sort(key=lambda row: row[0])
        
        # 构建输出文件名
        output_name = f"{parent_folder_name}_({time_range}).csv"
        csv_path = Path(output_folder) / output_name
        
        # 写入CSV文件
        with open(csv_path, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            writer.writerow(columns)
            writer.writerows(all_rows)
        
        print(f"✅ 合并导出成功: {csv_path} (共 {len(all_rows)} 条数据)")
        return True
    else:
        print(f"❌ 没有找到任何数据")
        return False

def calculate_time_range(time_base):
    """
    根据基准时间计算时间范围：
    - 开始时间：基准时间前两天
    - 结束时间：基准时间后一天
    
    参数:
        time_base: 字符串，格式为 "YYYY-MM-DD HH:MM:SS"
    
    返回:
        start_time, end_time: 两个字符串，格式同上
    """
    try:
        # 将字符串转换为datetime对象
        base_time = datetime.strptime(time_base, "%Y-%m-%d %H:%M:%S")
        
        # 计算开始时间（前两天）
        start_time = base_time - timedelta(days=2)
        
        # 计算结束时间（后一天）
        end_time = base_time + timedelta(days=1)
        
        # 转换回字符串格式
        return start_time.strftime("%Y-%m-%d %H:%M:%S"), end_time.strftime("%Y-%m-%d %H:%M:%S")
    
    except ValueError:
        raise ValueError("时间格式必须为 'YYYY-MM-DD HH:MM:SS'")

def generate_db_filename(date_time):
    """
    根据日期时间生成数据库文件名
    文件名格式为: 0YYMM.db (如2022年4月 -> 02204.db)
    """
    try:
        dt = datetime.strptime(date_time, "%Y-%m-%d %H:%M:%S")
        return f"0{dt.strftime('%y%m')}.db"
    except ValueError:
        raise ValueError("时间格式必须为 'YYYY-MM-DD HH:MM:SS'")

def is_cross_month(start_time, end_time):
    """
    判断时间范围是否跨越了月份
    
    参数:
        start_time: 开始时间字符串
        end_time: 结束时间字符串
    
    返回:
        是否跨月
    """
    start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
    end_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
    
    if start_dt.year == end_dt.year and start_dt.month == end_dt.month:
        return False
    return True

if __name__ == "__main__":
    # ===== 基础配置 =====
    BASE_PATH = r"D:\采数据\PROCESS"  # 数据库文件的根目录
    OUTPUT_FOLDER = r"D:\采数据\PROCESS\clean"  # 所有文件输出到此目录
    
    # ===== 井名和时间对应表 =====
    # 使用列表存储井名和时间的对应关系，支持同一井名的多个时间点
    WELLS_DATA_LIST = [
        # 格式: [井名, 时间]
        ["自201H35-2", "2022-03-04 17:33:08"],
        ["自201H35-3", "2022-10-10 17:48:53"],
        ["自201H54-3", "2022-04-20 06:05:28"],
        ["自201H54-4", "2022-03-15 07:59:56"],
        ["自201H54-6", "2022-07-09 12:46:49"],  # 第一个时间点
        ["自201H54-6", "2022-07-18 14:15:39"],  # 第二个时间点
        ["自201H56-4", "2022-10-07 03:48:36"],
        ["自201H62-5", "2023-01-12 04:33:08"],  # 第一个时间点
        ["自201H62-5", "2023-06-10 06:31:08"],  # 第二个时间点
        ["自201H62-8", "2022-12-20 19:12:22"],
        ["自201H69-5", "2022-10-05 04:10:12"],  # 第一个时间点
        ["自201H69-5", "2022-11-18 00:42:03"],  # 第二个时间点
        ["自201H69-6", "2023-01-26 09:03:58"],
        ["自201H69-8", "2022-08-31 11:44:17"],
        ["自201H69-11", "2024-01-07 02:12:29"],
        ["自201H70-2", "2022-04-12 18:12:10"],
        ["自205H1-4", "2022-06-23 18:33:53"],
        ["自205H1-7", "2022-05-24 15:56:09"],  # 第一个时间点
        ["自205H1-7", "2022-06-12 06:44:18"],  # 第二个时间点
        ["自205H1-8", "2023-08-18 11:51:46"],
        ["自205H2-4", "2023-08-27 04:22:39"],
        ["自227", "2023-10-17 04:13:16"],
        ["自230", "2023-12-17 19:34:38"],
        ["足203H4-4", "2022-12-31 21:49:41"],
        ["足203H6-2", "2022-02-15 11:21:41"],
    ]
    
    # 转换成字典用于向后兼容
    WELLS_DATA = {item[0]: item[1] for item in WELLS_DATA_LIST}
    
    # ===== 开始批处理 =====
    # 使用列表数据处理每一个井名和时间点
    results = []
    
    for well_data in WELLS_DATA_LIST:
        well_name, time_base = well_data
        
        print(f"\n{'='*50}")
        print(f"⏳ 正在处理: {well_name}, 基准时间: {time_base}")
        
        # 计算时间范围
        start_time, end_time = calculate_time_range(time_base)
        
        print(f"📅 时间范围: {start_time} 至 {end_time}")
        
        # 井的路径
        well_path = os.path.join(BASE_PATH, well_name)
        
        # 检查是否跨月
        if is_cross_month(start_time, end_time):
            print(f"🔄 检测到跨月时间范围，将查询多个月份的数据")
            success = export_filtered_data_cross_month(
                well_path=well_path,
                output_folder=OUTPUT_FOLDER,
                start_time=start_time,
                end_time=end_time,
                base_time=time_base
            )
        else:
            # 单月处理
            db_filename = generate_db_filename(time_base)
            db_path = os.path.join(well_path, db_filename)
            
            print(f"📂 数据库文件: {db_path}")
            
            success = export_filtered_data(
                db_file=db_path,
                output_folder=OUTPUT_FOLDER,
                start_time=start_time,
                end_time=end_time,
                base_time=time_base
            )
        
        # 为结果添加时间戳，以区分同一井名的不同时间点
        timestamp = datetime.strptime(time_base, "%Y-%m-%d %H:%M:%S").strftime("%y%m%d%H%M")
        results.append({
            "井名": f"{well_name}_{timestamp}",
            "基准时间": time_base,
            "时间范围": f"{start_time} 至 {end_time}",
            "跨月": "是" if is_cross_month(start_time, end_time) else "否",
            "处理结果": "成功" if success else "失败"
        })
    
    # 生成处理报告
    report_path = os.path.join(OUTPUT_FOLDER, "处理报告.xlsx")
    pd.DataFrame(results).to_excel(report_path, index=False)
    print(f"\n✅ 处理完成, 报告已保存至: {report_path}")

# 保留原来的process_wells函数，但不在本次处理中使用
# def process_wells(wells_data, base_path, output_folder):
#     """
#     批量处理多个井的数据
#     
#     参数:
#         wells_data: 字典，键为井名，值为基准时间
#         base_path: 根目录路径，格式如 "D:\\采数据\\PROCESS\\"
#         output_folder: 输出文件夹路径
#     """
#     results = []
#     
#     for well_name, time_base in wells_data.items():
#         print(f"\n{'='*50}")
#         print(f"⏳ 正在处理: {well_name}, 基准时间: {time_base}")
#         
#         # 生成数据库文件名
#         db_filename = generate_db_filename(time_base)
#         
#         # 组合完整路径
#         db_path = os.path.join(base_path, well_name, db_filename)
#         
#         # 计算时间范围
#         start_time, end_time = calculate_time_range(time_base)
#         
#         print(f"📅 时间范围: {start_time} 至 {end_time}")
#         print(f"📂 数据库文件: {db_path}")
#         
#         # 导出数据
#         success = export_filtered_data(
#             db_file=db_path,
#             output_folder=output_folder,
#             start_time=start_time,
#             end_time=end_time,
#             base_time=time_base
#         )
#         
#         results.append({
#             "井名": well_name,
#             "基准时间": time_base,
#             "数据库文件": db_path,
#             "处理结果": "成功" if success else "失败"
#         })
#     
#     # 生成处理报告
#     report_path = os.path.join(output_folder, "处理报告.xlsx")
#     pd.DataFrame(results).to_excel(report_path, index=False)
#     print(f"\n✅ 处理完成, 报告已保存至: {report_path}") 