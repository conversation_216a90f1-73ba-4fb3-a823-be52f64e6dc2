import sqlite3
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
from matplotlib.ticker import ScalarFormatter
from pylab import mpl
 
mpl.rcParams["font.sans-serif"] = ["SimHei"]
mpl.rcParams["axes.unicode_minus"] = False

def generate_trend_plot(db_files, output_folder, start_time, end_time):
    """整合脚本：直接读取数据库生成趋势图"""
    Path(output_folder).mkdir(parents=True, exist_ok=True)
    plt.style.use('seaborn-v0_8-colorblind')

    # 辅助函数：格式化时间范围
    def format_time_range(start, end):
        try:
            start_fmt = datetime.strptime(start, "%Y-%m-%d %H:%M:%S").strftime("%Y%m%d")
            end_fmt = datetime.strptime(end, "%Y-%m-%d %H:%M:%S").strftime("%Y%m%d")
            return f"{start_fmt}-{end_fmt}"
        except ValueError:
            raise ValueError("时间格式必须为 'YYYY-MM-DD HH:MM:SS'")

    time_range = format_time_range(start_time, end_time)

    for db_path in db_files:
        try:
            db_file = Path(db_path)
            parent_name = db_file.parent.name  # 获取父文件夹名 (如 自201H35-2)
            output_image = Path(output_folder) / f"{parent_name}_{time_range}.png"

            # ===== 数据库查询 =====
            conn = sqlite3.connect(str(db_file))
            df = pd.read_sql(f"""
                SELECT 
                    (WELLDATE || ' ' || WELLTIME) AS date,
                    DEP, BITDEP, HOKHEI, DRITIME, WOB, HKLD, RPM, TOR, SPP, CSIP
                FROM data
                WHERE datetime(WELLDATE || ' ' || WELLTIME) 
                    BETWEEN '{start_time}' AND '{end_time}'
            """, conn)
            conn.close()

            if df.empty:
                print(f"⏩ 无数据: {db_file.name}")
                continue

            # ===== 数据预处理 =====
            df['date'] = pd.to_datetime(df['date'])
            df['DEP'] = pd.to_numeric(df['DEP'], errors='coerce').ffill()

            # ===== 生成趋势图 =====
            features = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']
            fig, axes = plt.subplots(len(features), 1, figsize=(23, 1.3*len(features)), sharex=True)
            
            colors = plt.get_cmap('tab10')
            for i, col in enumerate(features):
                axes[i].plot(df['date'], df[col], color=colors(i%10), label=col)
                axes[i].set_ylabel(col, rotation=90, labelpad=20, ha='right')
                axes[i].legend(
                  loc='best',         # 自动选择图例位置（避免遮挡曲线）
                  frameon=True,     # 去掉图例边框（更简洁）
#                 fontsize='small'   # 调小字体（节省空间）
                  )
                axes[i].yaxis.set_major_formatter(ScalarFormatter(useOffset=False))
                axes[i].ticklabel_format(style='plain', axis='y')

            axes[-1].set_xlabel('时间')
            fig.suptitle(f'{parent_name} 特征趋势 ({time_range})', y=0.98)
            plt.tight_layout()
            
            # 保存图片
            plt.savefig(output_image, bbox_inches='tight', dpi=300)
            plt.close()
            print(f"✅ 生成成功: {output_image}")

        except sqlite3.Error as e:
            print(f"❌ 数据库错误: {str(e)}")
        except Exception as e:
            print(f"❌ 错误: {str(e)}")

if __name__ == "__main__":
    # ===== 配置参数 =====
    config = {
        "db_files": [
            r"D:\PyCharm\清洗3-4\自201H35-2\02203.db"  # 支持多个文件会生成多图
        ],
        "output_folder": r"D:\PyCharm\清洗3-4\趋势图输出",
        "start_time": "2022-03-04 00:00:00",
        "end_time": "2022-03-05 04:00:00"
    }

    # ===== 执行生成 =====
    generate_trend_plot(**config)